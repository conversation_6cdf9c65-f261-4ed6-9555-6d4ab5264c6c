<?php
/**
 * Plugin Name: WP Git Manager 2.0
 * Plugin URI: https://yourwebsite.com/wp-git-manager
 * Description: Adds Git functionality to WordPress admin bar for quick commits and repository management.
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Load Composer autoloader
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once __DIR__ . '/vendor/autoload.php';
}

class WPGitManager {
    
    private $plugin_url;
    private $plugin_path;
    
    public function __construct() {
        $this->plugin_url = plugin_dir_url(__FILE__);
        $this->plugin_path = plugin_dir_path(__FILE__);
        
        add_action('init', array($this, 'init'));
        add_action('admin_bar_menu', array($this, 'add_admin_bar_menu'), 100);
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_notices', array($this, 'admin_notices'));
        
        // AJAX handlers
        add_action('wp_ajax_git_commit', array($this, 'handle_git_commit'));
        add_action('wp_ajax_git_status', array($this, 'handle_git_status'));
        add_action('wp_ajax_git_push', array($this, 'handle_git_push'));
        add_action('wp_ajax_git_pull', array($this, 'handle_git_pull'));
        add_action('wp_ajax_git_setup_check', array($this, 'handle_git_setup_check'));
        add_action('wp_ajax_git_init_repo', array($this, 'handle_git_init_repo'));
        add_action('wp_ajax_git_add_remote', array($this, 'handle_git_add_remote'));
        add_action('wp_ajax_git_create_branch', array($this, 'handle_git_create_branch'));
        add_action('wp_ajax_git_set_user', array($this, 'handle_git_set_user'));
        add_action('wp_ajax_git_command', array($this, 'handle_git_command'));
        add_action('wp_ajax_git_init_repo_with_branch', array($this, 'handle_git_init_repo_with_branch'));
        add_action('wp_ajax_git_create_initial_commit', array($this, 'handle_git_create_initial_commit'));

        // Enhanced Git features
        add_action('wp_ajax_git_stage_file', array($this, 'handle_git_stage_file'));
        add_action('wp_ajax_git_unstage_file', array($this, 'handle_git_unstage_file'));
        add_action('wp_ajax_git_file_diff', array($this, 'handle_git_file_diff'));
        add_action('wp_ajax_git_commit_history', array($this, 'handle_git_commit_history'));
        add_action('wp_ajax_git_switch_branch', array($this, 'handle_git_switch_branch'));
        add_action('wp_ajax_git_get_branches', array($this, 'handle_git_get_branches'));

        // Repository management actions
        add_action('wp_ajax_git_reset_repository', array($this, 'handle_git_reset_repository'));
        add_action('wp_ajax_git_get_repository_info', array($this, 'handle_git_get_repository_info'));
        add_action('wp_ajax_git_stage_all', array($this, 'handle_git_stage_all'));
        add_action('wp_ajax_git_unstage_all', array($this, 'handle_git_unstage_all'));
        add_action('wp_ajax_git_get_remotes', array($this, 'handle_git_get_remotes'));
    }
    
    public function init() {
        // Check if setup is needed on admin pages
        if (is_admin() && current_user_can('manage_options')) {
            $setup_status = $this->check_setup_status();
            if (!$setup_status['is_complete']) {
                update_option('wpgm_needs_setup', true);
            }
        }
    }
    
    public function admin_notices() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        $needs_setup = get_option('wpgm_needs_setup', false);
        if ($needs_setup) {
            $setup_url = admin_url('admin.php?page=wp-git-manager-setup');
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p><strong>WP Git Manager:</strong> Git repository setup is incomplete. ';
            echo '<a href="' . esc_url($setup_url) . '">Complete setup now</a></p>';
            echo '</div>';
        }
    }
    
    public function add_admin_bar_menu($wp_admin_bar) {
        // Only show to users who can manage options and if setup is complete
        if (!current_user_can('manage_options') || get_option('wpgm_needs_setup', false)) {
            return;
        }
        
        $git_status = $this->get_git_status();
        $changes_count = $git_status['changes_count'] ?? 0;
        
        $title = 'Git';
        if ($changes_count > 0) {
            $title .= ' (' . $changes_count . ')';
        }
        
        $wp_admin_bar->add_menu(array(
            'id' => 'wp-git-manager',
            'title' => $title,
            'href' => '#',
            'meta' => array(
                'class' => 'wp-git-manager-button'
            )
        ));
        
        // Add submenu items
        $wp_admin_bar->add_menu(array(
            'parent' => 'wp-git-manager',
            'id' => 'git-commit',
            'title' => 'Commit Changes',
            'href' => '#',
            'meta' => array(
                'class' => 'git-commit-btn'
            )
        ));
        
        $wp_admin_bar->add_menu(array(
            'parent' => 'wp-git-manager',
            'id' => 'git-push',
            'title' => 'Push',
            'href' => '#',
            'meta' => array(
                'class' => 'git-push-btn'
            )
        ));
        
        $wp_admin_bar->add_menu(array(
            'parent' => 'wp-git-manager',
            'id' => 'git-pull',
            'title' => 'Pull',
            'href' => '#',
            'meta' => array(
                'class' => 'git-pull-btn'
            )
        ));

        $wp_admin_bar->add_menu(array(
            'parent' => 'wp-git-manager',
            'id' => 'git-dashboard',
            'title' => 'Dashboard',
            'href' => admin_url('admin.php?page=wp-git-manager')
        ));

        $wp_admin_bar->add_menu(array(
            'parent' => 'wp-git-manager',
            'id' => 'git-manage',
            'title' => 'Manage',
            'href' => admin_url('admin.php?page=wp-git-manager-manage')
        ));

        $wp_admin_bar->add_menu(array(
            'parent' => 'wp-git-manager',
            'id' => 'git-settings',
            'title' => 'Settings',
            'href' => admin_url('admin.php?page=wp-git-manager-settings')
        ));
    }
    
    public function add_admin_menu() {
        // Add main Git Manager menu
        add_menu_page(
            'Git Manager',
            'Git Manager',
            'manage_options',
            'wp-git-manager',
            array($this, 'dashboard_page'),
            'dashicons-admin-tools',
            30
        );

        // Add Dashboard submenu (default)
        add_submenu_page(
            'wp-git-manager',
            'Git Manager Dashboard',
            'Dashboard',
            'manage_options',
            'wp-git-manager',
            array($this, 'dashboard_page')
        );

        // Add Manage submenu
        add_submenu_page(
            'wp-git-manager',
            'Git Manager - Manage',
            'Manage',
            'manage_options',
            'wp-git-manager-manage',
            array($this, 'manage_page')
        );

        // Add Settings submenu
        add_submenu_page(
            'wp-git-manager',
            'Git Manager Settings',
            'Settings',
            'manage_options',
            'wp-git-manager-settings',
            array($this, 'settings_page')
        );

        // Add Setup submenu (hidden from menu but accessible via URL)
        add_submenu_page(
            null, // null parent means it won't show in menu but is accessible
            'Git Manager Setup',
            'Setup',
            'manage_options',
            'wp-git-manager-setup',
            array($this, 'setup_page')
        );
    }
    
    public function enqueue_scripts() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        wp_enqueue_script(
            'wp-git-manager-js',
            $this->plugin_url . 'assets/wp-git-manager.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_enqueue_style(
            'wp-git-manager-css',
            $this->plugin_url . 'assets/wp-git-manager.css',
            array(),
            '1.0.0'
        );
        
        wp_localize_script('wp-git-manager-js', 'wpGitManager', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wp_git_manager_nonce'),
            'strings' => array(
                'commit_message' => 'Enter commit message:',
                'commit_success' => 'Changes committed successfully!',
                'commit_error' => 'Error committing changes.',
                'push_success' => 'Changes pushed successfully!',
                'push_error' => 'Error pushing changes.',
                'pull_success' => 'Repository updated successfully!',
                'pull_error' => 'Error pulling changes.',
                'no_changes' => 'No changes to commit.',
                'confirm_push' => 'Are you sure you want to push changes?',
                'confirm_pull' => 'Are you sure you want to pull changes? This may overwrite local changes.',
                'setup_checking' => 'Checking Git setup...',
                'setup_error' => 'Setup check failed.',
                'initializing' => 'Initializing...',
                'success' => 'Success!',
                'error' => 'Error occurred.'
            )
        ));
    }
    
    // Include Git operations class
    private function get_git_status() {
        include_once $this->plugin_path . 'includes/class-git-operations.php';
        $git_ops = new WPGitManager_GitOperations();
        return $git_ops->get_status();
    }
    
    private function execute_git_command($command) {
        include_once $this->plugin_path . 'includes/class-git-operations.php';
        $git_ops = new WPGitManager_GitOperations();
        return $git_ops->execute_command($command);
    }
    
    private function check_setup_status() {
        include_once $this->plugin_path . 'includes/class-git-operations.php';
        $git_ops = new WPGitManager_GitOperations();
        return $git_ops->check_setup_status();
    }
    
    // Include AJAX handlers
    public function handle_git_commit() {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_commit();
    }
    
    public function handle_git_status() {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_status();
    }
    
    public function handle_git_push() {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_push();
    }
    
    public function handle_git_pull() {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_pull();
    }
    
    public function handle_git_setup_check() {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_setup_check();
    }
    
    public function handle_git_init_repo() {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_init_repo();
    }
    
    public function handle_git_add_remote() {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_add_remote();
    }
    
    public function handle_git_create_branch() {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_create_branch();
    }
    
    public function handle_git_set_user() {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_set_user();
    }

    public function handle_git_init_repo_with_branch()
    {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_init_repo_with_branch();
    }

    public function handle_git_create_initial_commit()
    {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_create_initial_commit();
    }

    public function handle_git_stage_file()
    {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_stage_file();
    }

    public function handle_git_unstage_file()
    {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_unstage_file();
    }

    public function handle_git_file_diff()
    {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_file_diff();
    }

    public function handle_git_commit_history()
    {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_commit_history();
    }

    public function handle_git_switch_branch()
    {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_switch_branch();
    }

    public function handle_git_get_branches()
    {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_get_branches();
    }

    public function handle_git_reset_repository()
    {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_reset_repository();
    }

    public function handle_git_get_repository_info()
    {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_get_repository_info();
    }

    public function handle_git_stage_all()
    {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_stage_all();
    }

    public function handle_git_unstage_all()
    {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_unstage_all();
    }

    public function handle_git_get_remotes()
    {
        include_once $this->plugin_path . 'includes/class-ajax-handlers.php';
        $ajax_handler = new WPGitManager_AjaxHandlers();
        $ajax_handler->handle_get_remotes();
    }



    // Dashboard page
    public function dashboard_page()
    {
        include $this->plugin_path . 'admin/dashboard-page.php';
    }

    // Manage page
    public function manage_page()
    {
        include $this->plugin_path . 'admin/manage-page.php';
    }

    // Settings page
    public function settings_page() {
        include $this->plugin_path . 'admin/settings-page.php';
    }

    // Setup page (for internal use)
    public function setup_page()
    {
        include $this->plugin_path . 'admin/setup-page.php';
    }
}

// Initialize the plugin
new WPGitManager();