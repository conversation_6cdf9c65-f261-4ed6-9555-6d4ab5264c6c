<?php

/**
 * Git Manager Dashboard Page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check user permissions
if (!current_user_can('manage_options')) {
    wp_die(__('You do not have sufficient permissions to access this page.'));
}

// Get Git operations class
include_once plugin_dir_path(__FILE__) . '../includes/class-git-operations.php';
$git_ops = new WPGitManager_GitOperations();

// Get repository information
$git_path = get_option('wpgm_git_path', '/usr/bin/git');
$repo_path = get_option('wpgm_repo_path', ABSPATH);
$status = $git_ops->get_status($git_path, $repo_path);
$branch_info = $git_ops->get_current_branch($git_path, $repo_path);
$commit_count = $git_ops->get_commit_count($git_path, $repo_path);
$recent_commits = $git_ops->get_recent_commits($git_path, $repo_path, 5);
$remotes = $git_ops->get_remotes($git_path, $repo_path);

?>

<div class="wrap">
    <h1>Git Manager Dashboard</h1>

    <div class="dashboard-widgets-wrap">
        <div id="dashboard-widgets" class="metabox-holder">

            <!-- Repository Overview -->
            <div class="postbox-container" style="width: 48%; float: left;">
                <div class="meta-box-sortables">

                    <!-- Repository Status Widget -->
                    <div class="postbox">
                        <div class="postbox-header">
                            <h2 class="hndle">Repository Status</h2>
                        </div>
                        <div class="inside">
                            <div class="git-status-overview">
                                <div class="status-item">
                                    <strong>Current Branch:</strong>
                                    <span class="branch-name"><?php echo esc_html($branch_info['current'] ?? 'Unknown'); ?></span>
                                </div>
                                <div class="status-item">
                                    <strong>Total Commits:</strong>
                                    <span class="commit-count"><?php echo esc_html($commit_count ?? '0'); ?></span>
                                </div>
                                <div class="status-item">
                                    <strong>Changed Files:</strong>
                                    <span class="changes-count"><?php echo esc_html($status['changes_count'] ?? '0'); ?></span>
                                </div>
                                <div class="status-item">
                                    <strong>Repository Path:</strong>
                                    <code><?php echo esc_html($repo_path); ?></code>
                                </div>
                            </div>

                            <?php if (!empty($status['files'])): ?>
                                <div class="git-changes-summary">
                                    <h4>Recent Changes</h4>
                                    <ul class="git-file-list">
                                        <?php foreach (array_slice($status['files'], 0, 5) as $file): ?>
                                            <li class="git-file-item">
                                                <span class="file-status status-<?php echo esc_attr(strtolower($file['status'])); ?>">
                                                    <?php echo esc_html($file['status']); ?>
                                                </span>
                                                <span class="file-name"><?php echo esc_html($file['name']); ?></span>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                    <?php if (count($status['files']) > 5): ?>
                                        <p><a href="<?php echo admin_url('admin.php?page=wp-git-manager-manage&tab=files'); ?>">View all <?php echo count($status['files']); ?> changed files</a></p>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Quick Actions Widget -->
                    <div class="postbox">
                        <div class="postbox-header">
                            <h2 class="hndle">Quick Actions</h2>
                        </div>
                        <div class="inside">
                            <div class="git-quick-actions">
                                <p>
                                    <button type="button" class="button button-primary git-commit-btn">
                                        <span class="dashicons dashicons-yes"></span> Commit Changes
                                    </button>
                                    <button type="button" class="button git-push-btn">
                                        <span class="dashicons dashicons-upload"></span> Push
                                    </button>
                                    <button type="button" class="button git-pull-btn">
                                        <span class="dashicons dashicons-download"></span> Pull
                                    </button>
                                </p>
                                <p>
                                    <button type="button" class="button" id="refresh-status">
                                        <span class="dashicons dashicons-update"></span> Refresh Status
                                    </button>
                                    <a href="<?php echo admin_url('admin.php?page=wp-git-manager-manage'); ?>" class="button">
                                        <span class="dashicons dashicons-admin-tools"></span> Manage Repository
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Recent Activity -->
            <div class="postbox-container" style="width: 48%; float: right;">
                <div class="meta-box-sortables">

                    <!-- Recent Commits Widget -->
                    <div class="postbox">
                        <div class="postbox-header">
                            <h2 class="hndle">Recent Commits</h2>
                        </div>
                        <div class="inside">
                            <?php if (!empty($recent_commits)): ?>
                                <div class="git-commits-list">
                                    <?php foreach ($recent_commits as $commit): ?>
                                        <div class="commit-item">
                                            <div class="commit-hash">
                                                <code><?php echo esc_html(substr($commit['hash'], 0, 8)); ?></code>
                                            </div>
                                            <div class="commit-message">
                                                <?php echo esc_html($commit['message']); ?>
                                            </div>
                                            <div class="commit-meta">
                                                <span class="commit-author"><?php echo esc_html($commit['author']); ?></span>
                                                <span class="commit-date"><?php echo esc_html($commit['date']); ?></span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <p><a href="<?php echo admin_url('admin.php?page=wp-git-manager-manage&tab=history'); ?>">View full commit history</a></p>
                            <?php else: ?>
                                <p>No commits found in this repository.</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Remote Repositories Widget -->
                    <div class="postbox">
                        <div class="postbox-header">
                            <h2 class="hndle">Remote Repositories</h2>
                        </div>
                        <div class="inside">
                            <?php if (!empty($remotes)): ?>
                                <div class="git-remotes-list">
                                    <?php foreach ($remotes as $remote): ?>
                                        <div class="remote-item">
                                            <strong><?php echo esc_html($remote['name']); ?></strong><br>
                                            <code><?php echo esc_html($remote['url']); ?></code>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <p>No remote repositories configured.</p>
                                <p><a href="<?php echo admin_url('admin.php?page=wp-git-manager-manage&tab=remotes'); ?>">Add remote repository</a></p>
                            <?php endif; ?>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </div>

    <div class="clear"></div>

    <!-- Status Output Area -->
    <div id="git-status-output" style="margin-top: 20px;"></div>

</div>

<style>
    .git-status-overview .status-item {
        margin-bottom: 10px;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }

    .git-status-overview .status-item:last-child {
        border-bottom: none;
    }

    .branch-name {
        background: #0073aa;
        color: white;
        padding: 2px 8px;
        border-radius: 3px;
        font-size: 12px;
    }

    .commit-count,
    .changes-count {
        font-weight: bold;
        color: #0073aa;
    }

    .git-file-item {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        padding: 5px 0;
    }

    .file-status {
        display: inline-block;
        width: 20px;
        text-align: center;
        font-weight: bold;
        margin-right: 10px;
        font-size: 12px;
    }

    .status-m {
        color: #d63638;
    }

    /* Modified */
    .status-a {
        color: #00a32a;
    }

    /* Added */
    .status-d {
        color: #d63638;
    }

    /* Deleted */
    .status-r {
        color: #dba617;
    }

    /* Renamed */

    .commit-item {
        margin-bottom: 15px;
        padding: 10px;
        background: #f9f9f9;
        border-left: 3px solid #0073aa;
    }

    .commit-hash {
        font-family: monospace;
        margin-bottom: 5px;
    }

    .commit-message {
        font-weight: bold;
        margin-bottom: 5px;
    }

    .commit-meta {
        font-size: 12px;
        color: #666;
    }

    .commit-author {
        margin-right: 15px;
    }

    .remote-item {
        margin-bottom: 10px;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }

    .remote-item:last-child {
        border-bottom: none;
    }

    .git-quick-actions .button {
        margin-right: 5px;
        margin-bottom: 5px;
    }

    .git-quick-actions .dashicons {
        margin-right: 5px;
    }
</style>

<script>
    jQuery(document).ready(function($) {
        // Refresh status functionality
        $('#refresh-status').on('click', function() {
            var button = $(this);
            button.prop('disabled', true).text('Refreshing...');

            $.post(ajaxurl, {
                action: 'git_status',
                nonce: '<?php echo wp_create_nonce('wp_git_manager_nonce'); ?>'
            }, function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error refreshing status: ' + response.data);
                }
            }).always(function() {
                button.prop('disabled', false).html('<span class="dashicons dashicons-update"></span> Refresh Status');
            });
        });
    });
</script>