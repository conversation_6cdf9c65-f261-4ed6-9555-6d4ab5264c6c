jQuery(document).ready(function($) {
    'use strict';
    
    // Git Manager functionality
    const GitManager = {
        
        init: function() {
            $ = jQuery;
            this.bindEvents();
            this.updateStatus();
        },
        
        bindEvents: function() {
            // Admin bar menu items
            $('.git-commit-btn').on('click', this.handleCommit);
            $('.git-push-btn').on('click', this.handlePush);
            $('.git-pull-btn').on('click', this.handlePull);
            
            // Enhanced features
            $(document).on('click', '.git-branch-switch', this.handleBranchSwitch);
            $(document).on('click', '.git-stage-file', this.handleStageFile);
            $(document).on('click', '.git-unstage-file', this.handleUnstageFile);
            $(document).on('click', '.git-view-diff', this.handleViewDiff);
            $(document).on('click', '.git-view-history', this.handleViewHistory);
            
            // Setup page events
            $(document).on('click', '#test-git-path', this.testGitPath);
            $(document).on('click', '#init-repo', this.initRepo);
            $(document).on('click', '#set-user', this.setUser);
            $(document).on('click', '#add-remote', this.addRemote);
            $(document).on('click', '#create-branch', this.createBranch);
            $(document).on('click', '#save-settings', this.saveSettings);
        },
        
        updateStatus: function() {
            this.ajaxRequest('git_status', {}, function(response) {
                if (response.success) {
                    const status = response.data;
                    $('.git-status-info').html(
                        '<strong>Changes:</strong> ' + status.changes_count + 
                        ' | <strong>Status:</strong> ' + status.status
                    );
                    
                    if (status.detailed_status) {
                        GitManager.updateFileList(status.detailed_status);
                    }
                }
            });
        },
        
        updateFileList: function(status) {
            let html = '';
            
            if (status.added && status.added.length > 0) {
                html += '<h4>Added Files:</h4><ul>';
                status.added.forEach(function(file) {
                    html += '<li class="git-file-added">' + file + 
                           ' <button class="git-unstage-file" data-file="' + file + '">Unstage</button></li>';
                });
                html += '</ul>';
            }
            
            if (status.modified && status.modified.length > 0) {
                html += '<h4>Modified Files:</h4><ul>';
                status.modified.forEach(function(file) {
                    html += '<li class="git-file-modified">' + file + 
                           ' <button class="git-stage-file" data-file="' + file + '">Stage</button>' +
                           ' <button class="git-view-diff" data-file="' + file + '">View Diff</button></li>';
                });
                html += '</ul>';
            }
            
            if (status.deleted && status.deleted.length > 0) {
                html += '<h4>Deleted Files:</h4><ul>';
                status.deleted.forEach(function(file) {
                    html += '<li class="git-file-deleted">' + file + 
                           ' <button class="git-stage-file" data-file="' + file + '">Stage</button></li>';
                });
                html += '</ul>';
            }
            
            $('.git-file-list').html(html);
        },
        
        handleCommit: function(e) {
            e.preventDefault();
            
            const message = prompt(wpGitManager.strings.commit_message);
            if (!message) return;
            
            GitManager.ajaxRequest('git_commit', { message: message }, function(response) {
                if (response.success) {
                    GitManager.showNotice(wpGitManager.strings.commit_success, 'success');
                    GitManager.updateStatus();
                } else {
                    GitManager.showNotice(wpGitManager.strings.commit_error + ': ' + response.data, 'error');
                }
            });
        },
        
        handlePush: function(e) {
            e.preventDefault();
            
            if (!confirm(wpGitManager.strings.confirm_push)) return;
            
            GitManager.ajaxRequest('git_push', {}, function(response) {
                if (response.success) {
                    GitManager.showNotice(wpGitManager.strings.push_success, 'success');
                } else {
                    GitManager.showNotice(wpGitManager.strings.push_error + ': ' + response.data, 'error');
                }
            });
        },
        
        handlePull: function(e) {
            e.preventDefault();
            
            if (!confirm(wpGitManager.strings.confirm_pull)) return;
            
            GitManager.ajaxRequest('git_pull', {}, function(response) {
                if (response.success) {
                    GitManager.showNotice(wpGitManager.strings.pull_success, 'success');
                    GitManager.updateStatus();
                } else {
                    GitManager.showNotice(wpGitManager.strings.pull_error + ': ' + response.data, 'error');
                }
            });
        },
        
        handleStageFile: function(e) {
            e.preventDefault();
            const file = $(this).data('file');
            
            GitManager.ajaxRequest('git_stage_file', { file: file }, function(response) {
                if (response.success) {
                    GitManager.showNotice('File staged: ' + file, 'success');
                    GitManager.updateStatus();
                } else {
                    GitManager.showNotice('Error staging file: ' + response.data, 'error');
                }
            });
        },
        
        handleUnstageFile: function(e) {
            e.preventDefault();
            const file = $(this).data('file');
            
            GitManager.ajaxRequest('git_unstage_file', { file: file }, function(response) {
                if (response.success) {
                    GitManager.showNotice('File unstaged: ' + file, 'success');
                    GitManager.updateStatus();
                } else {
                    GitManager.showNotice('Error unstaging file: ' + response.data, 'error');
                }
            });
        },
        
        handleViewDiff: function(e) {
            e.preventDefault();
            const file = $(this).data('file');
            
            GitManager.ajaxRequest('git_file_diff', { file: file }, function(response) {
                if (response.success) {
                    GitManager.showDiffModal(file, response.data.diff);
                } else {
                    GitManager.showNotice('Error getting diff: ' + response.data, 'error');
                }
            });
        },
        
        handleViewHistory: function(e) {
            e.preventDefault();
            
            GitManager.ajaxRequest('git_commit_history', {}, function(response) {
                if (response.success) {
                    GitManager.showHistoryModal(response.data.commits);
                } else {
                    GitManager.showNotice('Error getting history: ' + response.data, 'error');
                }
            });
        },
        
        handleBranchSwitch: function(e) {
            e.preventDefault();
            const branch = $(this).data('branch');
            
            GitManager.ajaxRequest('git_switch_branch', { branch: branch }, function(response) {
                if (response.success) {
                    GitManager.showNotice('Switched to branch: ' + branch, 'success');
                    location.reload();
                } else {
                    GitManager.showNotice('Error switching branch: ' + response.data, 'error');
                }
            });
        },
        
        showDiffModal: function(file, diff) {
            const modal = $('<div class="git-modal-overlay"><div class="git-modal">' +
                          '<div class="git-modal-header"><h3>Diff: ' + file + '</h3>' +
                          '<button class="git-modal-close">&times;</button></div>' +
                          '<div class="git-modal-content"><pre>' + diff + '</pre></div>' +
                          '</div></div>');
            
            $('body').append(modal);
            modal.find('.git-modal-close').on('click', function() {
                modal.remove();
            });
        },
        
        showHistoryModal: function(commits) {
            let html = '<ul>';
            commits.forEach(function(commit) {
                html += '<li>' + commit + '</li>';
            });
            html += '</ul>';
            
            const modal = $('<div class="git-modal-overlay"><div class="git-modal">' +
                          '<div class="git-modal-header"><h3>Commit History</h3>' +
                          '<button class="git-modal-close">&times;</button></div>' +
                          '<div class="git-modal-content">' + html + '</div>' +
                          '</div></div>');
            
            $('body').append(modal);
            modal.find('.git-modal-close').on('click', function() {
                modal.remove();
            });
        },
        
        showNotice: function(message, type) {
            const notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            $('.wrap').prepend(notice);
            
            setTimeout(function() {
                notice.fadeOut();
            }, 5000);
        },
        
        ajaxRequest: function(action, data, callback) {
            const requestData = {
                action: action,
                nonce: wpGitManager.nonce,
                ...data
            };
            
            $.post(wpGitManager.ajax_url, requestData, callback);
        },
        
        // Setup page methods
        testGitPath: function(e) {
            e.preventDefault();
            const gitPath = $('#git-path').val();
            
            GitManager.ajaxRequest('git_setup_check', {
                check_type: 'git_path',
                git_path: gitPath
            }, function(response) {
                if (response.success) {
                    GitManager.showNotice('Git found: ' + response.data.version, 'success');
                } else {
                    GitManager.showNotice('Git test failed: ' + response.data, 'error');
                }
            });
        },
        
        initRepo: function(e) {
            e.preventDefault();
            const gitPath = $('#git-path').val();
            const repoPath = $('#repo-path').val();
            
            GitManager.ajaxRequest('git_init_repo', {
                git_path: gitPath,
                repo_path: repoPath
            }, function(response) {
                if (response.success) {
                    GitManager.showNotice(response.data, 'success');
                } else {
                    GitManager.showNotice('Init failed: ' + response.data, 'error');
                }
            });
        },
        
        setUser: function(e) {
            e.preventDefault();
            const gitPath = $('#git-path').val();
            const repoPath = $('#repo-path').val();
            const userName = $('#user-name').val();
            const userEmail = $('#user-email').val();
            
            GitManager.ajaxRequest('git_set_user', {
                git_path: gitPath,
                repo_path: repoPath,
                user_name: userName,
                user_email: userEmail
            }, function(response) {
                if (response.success) {
                    GitManager.showNotice(response.data, 'success');
                } else {
                    GitManager.showNotice('User config failed: ' + response.data, 'error');
                }
            });
        },
        
        addRemote: function(e) {
            e.preventDefault();
            const gitPath = $('#git-path').val();
            const repoPath = $('#repo-path').val();
            const remoteName = $('#remote-name').val();
            const remoteUrl = $('#remote-url').val();
            
            GitManager.ajaxRequest('git_add_remote', {
                git_path: gitPath,
                repo_path: repoPath,
                remote_name: remoteName,
                remote_url: remoteUrl
            }, function(response) {
                if (response.success) {
                    GitManager.showNotice(response.data, 'success');
                } else {
                    GitManager.showNotice('Add remote failed: ' + response.data, 'error');
                }
            });
        },
        
        createBranch: function(e) {
            e.preventDefault();
            const gitPath = $('#git-path').val();
            const repoPath = $('#repo-path').val();
            const branchName = $('#branch-name').val();
            
            GitManager.ajaxRequest('git_create_branch', {
                git_path: gitPath,
                repo_path: repoPath,
                branch_name: branchName
            }, function(response) {
                if (response.success) {
                    GitManager.showNotice(response.data, 'success');
                } else {
                    GitManager.showNotice('Create branch failed: ' + response.data, 'error');
                }
            });
        },
        
        saveSettings: function(e) {
            e.preventDefault();
            
            const settings = {
                git_path: $('#git-path').val(),
                repo_path: $('#repo-path').val(),
                remote_name: $('#remote-name').val(),
                branch_name: $('#branch-name').val()
            };
            
            GitManager.ajaxRequest('git_setup_check', {
                check_type: 'save_settings',
                settings: settings
            }, function(response) {
                if (response.success) {
                    GitManager.showNotice('Settings saved successfully!', 'success');
                } else {
                    GitManager.showNotice('Save failed: ' + response.data, 'error');
                }
            });
        }
    };
    
    // Initialize Git Manager
    GitManager.init();

    // Initialize Manage Page if we're on that page
    if (window.location.href.indexOf('wp-git-manager-manage') !== -1) {
        ManagePage.init();
    }
});

// Manage Page functionality
const ManagePage = {

    init: function () {
        $ = jQuery;
        this.bindEvents();
    },

    bindEvents: function () {
        // File management
        $('#select-all-files').on('change', this.handleSelectAll);
        $(document).on('click', '.git-stage-file', this.handleStageFile);
        $(document).on('click', '.git-unstage-file', this.handleUnstageFile);
        $(document).on('click', '.git-view-diff', this.handleViewDiff);
        $('#stage-all').on('click', this.handleStageAll);
        $('#unstage-all').on('click', this.handleUnstageAll);

        // Commit operations
        $('#create-commit').on('click', this.handleCreateCommit);

        // Branch management
        $('#create-branch').on('click', this.handleCreateBranch);
        $('#load-branches').on('click', this.handleLoadBranches);

        // Remote management
        $('#add-remote').on('click', this.handleAddRemote);
        $('#load-remotes').on('click', this.handleLoadRemotes);

        // History
        $('#load-history').on('click', this.handleLoadHistory);

        // Advanced operations
        $('#view-git-log').on('click', this.handleViewLog);
        $('#view-git-status').on('click', this.handleViewStatus);
        $('#view-git-config').on('click', this.handleViewConfig);
        $('#git-gc').on('click', this.handleGarbageCollection);
        $('#git-fsck').on('click', this.handleFileSystemCheck);
        $('#reset-hard').on('click', this.handleResetHard);
        $('#clean-untracked').on('click', this.handleCleanUntracked);
    },

    handleSelectAll: function () {
        const isChecked = $(this).is(':checked');
        $('input[name="selected_files[]"]').prop('checked', isChecked);
    },

    handleStageFile: function (e) {
        e.preventDefault();
        const file = $(this).data('file');
        ManagePage.stageFile(file);
    },

    handleUnstageFile: function (e) {
        e.preventDefault();
        const file = $(this).data('file');
        ManagePage.unstageFile(file);
    },

    handleViewDiff: function (e) {
        e.preventDefault();
        const file = $(this).data('file');
        ManagePage.viewDiff(file);
    },

    handleStageAll: function (e) {
        e.preventDefault();
        ManagePage.stageAll();
    },

    handleUnstageAll: function (e) {
        e.preventDefault();
        ManagePage.unstageAll();
    },

    handleCreateCommit: function (e) {
        e.preventDefault();
        const message = $('#commit-message').val();
        const autoStage = $('#auto-stage').is(':checked');

        if (!message.trim()) {
            alert('Please enter a commit message.');
            return;
        }

        ManagePage.createCommit(message, autoStage);
    },

    handleCreateBranch: function (e) {
        e.preventDefault();
        const branchName = $('#new-branch-name').val();

        if (!branchName.trim()) {
            alert('Please enter a branch name.');
            return;
        }

        ManagePage.createBranch(branchName);
    },

    handleLoadBranches: function (e) {
        e.preventDefault();
        ManagePage.loadBranches();
    },

    handleAddRemote: function (e) {
        e.preventDefault();
        const name = $('#remote-name').val();
        const url = $('#remote-url').val();

        if (!name.trim() || !url.trim()) {
            alert('Please enter both remote name and URL.');
            return;
        }

        ManagePage.addRemote(name, url);
    },

    handleLoadRemotes: function (e) {
        e.preventDefault();
        ManagePage.loadRemotes();
    },

    handleLoadHistory: function (e) {
        e.preventDefault();
        const limit = $('#history-limit').val();
        ManagePage.loadHistory(limit);
    },

    handleViewLog: function (e) {
        e.preventDefault();
        ManagePage.executeGitCommand('log --oneline -10');
    },

    handleViewStatus: function (e) {
        e.preventDefault();
        ManagePage.executeGitCommand('status');
    },

    handleViewConfig: function (e) {
        e.preventDefault();
        ManagePage.executeGitCommand('config --list');
    },

    handleGarbageCollection: function (e) {
        e.preventDefault();
        if (confirm('Run garbage collection? This will optimize the repository.')) {
            ManagePage.executeGitCommand('gc');
        }
    },

    handleFileSystemCheck: function (e) {
        e.preventDefault();
        if (confirm('Run file system check? This will verify repository integrity.')) {
            ManagePage.executeGitCommand('fsck');
        }
    },

    handleResetHard: function (e) {
        e.preventDefault();
        if (confirm('WARNING: This will permanently discard all uncommitted changes. Are you sure?')) {
            ManagePage.executeGitCommand('reset --hard HEAD');
        }
    },

    handleCleanUntracked: function (e) {
        e.preventDefault();
        if (confirm('WARNING: This will permanently delete all untracked files. Are you sure?')) {
            ManagePage.executeGitCommand('clean -fd');
        }
    },

    // API methods
    stageFile: function (file) {
        this.showLoading('Staging file...');

        $.post(wpGitManager.ajax_url, {
            action: 'git_stage_file',
            file: file,
            nonce: wpGitManager.nonce
        }, function (response) {
            ManagePage.hideLoading();
            if (response.success) {
                ManagePage.showMessage('File staged successfully', 'success');
                location.reload();
            } else {
                ManagePage.showMessage('Error staging file: ' + response.data, 'error');
            }
        });
    },

    unstageFile: function (file) {
        this.showLoading('Unstaging file...');

        $.post(wpGitManager.ajax_url, {
            action: 'git_unstage_file',
            file: file,
            nonce: wpGitManager.nonce
        }, function (response) {
            ManagePage.hideLoading();
            if (response.success) {
                ManagePage.showMessage('File unstaged successfully', 'success');
                location.reload();
            } else {
                ManagePage.showMessage('Error unstaging file: ' + response.data, 'error');
            }
        });
    },

    stageAll: function () {
        this.showLoading('Staging all files...');

        $.post(wpGitManager.ajax_url, {
            action: 'git_stage_all',
            nonce: wpGitManager.nonce
        }, function (response) {
            ManagePage.hideLoading();
            if (response.success) {
                ManagePage.showMessage('All files staged successfully', 'success');
                location.reload();
            } else {
                ManagePage.showMessage('Error staging files: ' + response.data, 'error');
            }
        });
    },

    unstageAll: function () {
        this.showLoading('Unstaging all files...');

        $.post(wpGitManager.ajax_url, {
            action: 'git_unstage_all',
            nonce: wpGitManager.nonce
        }, function (response) {
            ManagePage.hideLoading();
            if (response.success) {
                ManagePage.showMessage('All files unstaged successfully', 'success');
                location.reload();
            } else {
                ManagePage.showMessage('Error unstaging files: ' + response.data, 'error');
            }
        });
    },

    viewDiff: function (file) {
        this.showLoading('Loading diff...');

        $.post(wpGitManager.ajax_url, {
            action: 'git_file_diff',
            file: file,
            nonce: wpGitManager.nonce
        }, function (response) {
            ManagePage.hideLoading();
            if (response.success) {
                ManagePage.showDiff(file, response.data.diff);
            } else {
                ManagePage.showMessage('Error loading diff: ' + response.data, 'error');
            }
        });
    },

    createCommit: function (message, autoStage) {
        this.showLoading('Creating commit...');

        $.post(wpGitManager.ajax_url, {
            action: 'git_commit',
            message: message,
            auto_stage: autoStage,
            nonce: wpGitManager.nonce
        }, function (response) {
            ManagePage.hideLoading();
            if (response.success) {
                ManagePage.showMessage('Commit created successfully', 'success');
                $('#commit-message').val('');
                location.reload();
            } else {
                ManagePage.showMessage('Error creating commit: ' + response.data, 'error');
            }
        });
    },

    createBranch: function (branchName) {
        this.showLoading('Creating branch...');

        $.post(wpGitManager.ajax_url, {
            action: 'git_create_branch',
            branch_name: branchName,
            nonce: wpGitManager.nonce
        }, function (response) {
            ManagePage.hideLoading();
            if (response.success) {
                ManagePage.showMessage('Branch created successfully', 'success');
                $('#new-branch-name').val('');
                ManagePage.loadBranches();
            } else {
                ManagePage.showMessage('Error creating branch: ' + response.data, 'error');
            }
        });
    },

    loadBranches: function () {
        this.showLoading('Loading branches...');

        $.post(wpGitManager.ajax_url, {
            action: 'git_get_branches',
            nonce: wpGitManager.nonce
        }, function (response) {
            ManagePage.hideLoading();
            if (response.success) {
                ManagePage.displayBranches(response.data);
            } else {
                ManagePage.showMessage('Error loading branches: ' + response.data, 'error');
            }
        });
    },

    addRemote: function (name, url) {
        this.showLoading('Adding remote...');

        $.post(wpGitManager.ajax_url, {
            action: 'git_add_remote',
            remote_name: name,
            remote_url: url,
            nonce: wpGitManager.nonce
        }, function (response) {
            ManagePage.hideLoading();
            if (response.success) {
                ManagePage.showMessage('Remote added successfully', 'success');
                $('#remote-name').val('');
                $('#remote-url').val('');
                ManagePage.loadRemotes();
            } else {
                ManagePage.showMessage('Error adding remote: ' + response.data, 'error');
            }
        });
    },

    loadRemotes: function () {
        this.showLoading('Loading remotes...');

        $.post(wpGitManager.ajax_url, {
            action: 'git_get_remotes',
            nonce: wpGitManager.nonce
        }, function (response) {
            ManagePage.hideLoading();
            if (response.success) {
                ManagePage.displayRemotes(response.data);
            } else {
                ManagePage.showMessage('Error loading remotes: ' + response.data, 'error');
            }
        });
    },

    loadHistory: function (limit) {
        this.showLoading('Loading commit history...');

        $.post(wpGitManager.ajax_url, {
            action: 'git_commit_history',
            limit: limit,
            nonce: wpGitManager.nonce
        }, function (response) {
            ManagePage.hideLoading();
            if (response.success) {
                ManagePage.displayHistory(response.data.commits);
            } else {
                ManagePage.showMessage('Error loading history: ' + response.data, 'error');
            }
        });
    },

    executeGitCommand: function (command) {
        this.showLoading('Executing command...');

        $.post(wpGitManager.ajax_url, {
            action: 'git_execute_command',
            command: command,
            nonce: wpGitManager.nonce
        }, function (response) {
            ManagePage.hideLoading();
            if (response.success) {
                ManagePage.showOutput(command, response.data);
            } else {
                ManagePage.showMessage('Error executing command: ' + response.data, 'error');
            }
        });
    },

    // UI helper methods
    showLoading: function (message) {
        $('#git-output').html('<div class="notice notice-info"><p>' + message + '</p></div>');
    },

    hideLoading: function () {
        // Loading will be replaced by actual content
    },

    showMessage: function (message, type) {
        const className = type === 'success' ? 'notice-success' : 'notice-error';
        $('#git-output').html('<div class="notice ' + className + ' is-dismissible"><p>' + message + '</p></div>');
    },

    showOutput: function (command, output) {
        const html = '<div class="git-command-output">' +
            '<h4>Command: git ' + command + '</h4>' +
            '<pre>' + output + '</pre>' +
            '</div>';
        $('#git-output').html(html);
    },

    showDiff: function (file, diff) {
        const html = '<div class="git-diff-output">' +
            '<h4>Diff for: ' + file + '</h4>' +
            '<pre class="diff-content">' + diff + '</pre>' +
            '</div>';
        $('#git-output').html(html);
    },

    displayBranches: function (data) {
        let html = '<table class="wp-list-table widefat fixed striped">';
        html += '<thead><tr><th>Branch</th><th>Current</th><th>Actions</th></tr></thead><tbody>';

        if (data.branches && data.branches.length > 0) {
            data.branches.forEach(function (branch) {
                const isCurrent = branch === data.current_branch;
                html += '<tr>';
                html += '<td><code>' + branch + '</code></td>';
                html += '<td>' + (isCurrent ? '<strong>✓ Current</strong>' : '') + '</td>';
                html += '<td>';
                if (!isCurrent) {
                    html += '<button class="button button-small git-switch-branch" data-branch="' + branch + '">Switch</button>';
                }
                html += '</td>';
                html += '</tr>';
            });
        } else {
            html += '<tr><td colspan="3">No branches found</td></tr>';
        }

        html += '</tbody></table>';
        $('#branch-list-content').html(html);

        // Bind switch branch events
        $('.git-switch-branch').on('click', function (e) {
            e.preventDefault();
            const branch = $(this).data('branch');
            if (confirm('Switch to branch: ' + branch + '?')) {
                ManagePage.switchBranch(branch);
            }
        });
    },

    displayRemotes: function (remotes) {
        let html = '<table class="wp-list-table widefat fixed striped">';
        html += '<thead><tr><th>Name</th><th>URL</th><th>Actions</th></tr></thead><tbody>';

        if (remotes && remotes.length > 0) {
            remotes.forEach(function (remote) {
                html += '<tr>';
                html += '<td><strong>' + remote.name + '</strong></td>';
                html += '<td><code>' + remote.url + '</code></td>';
                html += '<td><button class="button button-small button-link-delete">Remove</button></td>';
                html += '</tr>';
            });
        } else {
            html += '<tr><td colspan="3">No remotes configured</td></tr>';
        }

        html += '</tbody></table>';
        $('#remote-list-content').html(html);
    },

    displayHistory: function (commits) {
        let html = '';

        if (commits && commits.length > 0) {
            commits.forEach(function (commit) {
                html += '<div class="commit-item">';
                html += '<div class="commit-hash"><code>' + commit.substring(0, 8) + '</code></div>';
                html += '<div class="commit-message">' + commit.substring(9) + '</div>';
                html += '</div>';
            });
        } else {
            html = '<p>No commits found in repository.</p>';
        }

        $('#commit-history').html(html);
    },

    switchBranch: function (branch) {
        this.showLoading('Switching branch...');

        $.post(wpGitManager.ajax_url, {
            action: 'git_switch_branch',
            branch: branch,
            nonce: wpGitManager.nonce
        }, function (response) {
            ManagePage.hideLoading();
            if (response.success) {
                ManagePage.showMessage('Switched to branch: ' + branch, 'success');
                ManagePage.loadBranches();
            } else {
                ManagePage.showMessage('Error switching branch: ' + response.data, 'error');
            }
        });
    }
};
